{"ast": null, "code": "import _regeneratorRuntime from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u533A\\u5757\\u94FE\\u9879\\u76EE/\\u4EA4\\u6613\\u6240\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u533A\\u5757\\u94FE\\u9879\\u76EE/\\u4EA4\\u6613\\u6240\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _objectSpread from \"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u533A\\u5757\\u94FE\\u9879\\u76EE/\\u4EA4\\u6613\\u6240\\u9879\\u76EE/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport { mapGetters } from 'vuex';\nimport { updateUserInfo, updatePassword } from '@/api/auth';\nimport { getNavMenus } from '@/api/menu'; // 导入获取菜单的API\n\nexport default {\n  name: 'Layout',\n  data: function data() {\n    var _this = this;\n    return {\n      isCollapse: false,\n      userInfoVisible: false,\n      passwordVisible: false,\n      menus: [],\n      // 存储菜单数据\n      userForm: {\n        avatar: '',\n        username: '',\n        nickname: '',\n        phone: '',\n        email: ''\n      },\n      userRules: {\n        nickname: [{\n          required: true,\n          message: '请输入昵称',\n          trigger: 'blur'\n        }],\n        phone: [{\n          required: true,\n          message: '请输入手机号',\n          trigger: 'blur'\n        }, {\n          pattern: /^1[3-9]\\d{9}$/,\n          message: '请输入正确的手机号',\n          trigger: 'blur'\n        }],\n        email: [{\n          required: true,\n          message: '请输入邮箱',\n          trigger: 'blur'\n        }, {\n          type: 'email',\n          message: '请输入正确的邮箱地址',\n          trigger: 'blur'\n        }]\n      },\n      passwordForm: {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      },\n      passwordRules: {\n        oldPassword: [{\n          required: true,\n          message: '请输原密码',\n          trigger: 'blur'\n        }, {\n          min: 6,\n          message: '密码长度不能小于6位',\n          trigger: 'blur'\n        }],\n        newPassword: [{\n          required: true,\n          message: '请输入新密码',\n          trigger: 'blur'\n        }, {\n          min: 6,\n          message: '密码长度不能小于6位',\n          trigger: 'blur'\n        }, {\n          validator: function validator(rule, value, callback) {\n            if (value === _this.passwordForm.oldPassword) {\n              callback(new Error('新密不能与原密码相同'));\n            } else {\n              callback();\n            }\n          },\n          trigger: 'blur'\n        }],\n        confirmPassword: [{\n          required: true,\n          message: '请再次输入新密码',\n          trigger: 'blur'\n        }, {\n          validator: this.validateConfirmPassword,\n          trigger: 'blur'\n        }]\n      },\n      loading: false\n    };\n  },\n  computed: _objectSpread({}, mapGetters(['userInfo'])),\n  created: function created() {\n    // 组件创建时获取菜单数据\n    this.getMenus();\n  },\n  methods: {\n    toggleSidebar: function toggleSidebar() {\n      this.isCollapse = !this.isCollapse;\n    },\n    handleCommand: function handleCommand(command) {\n      var _this2 = this;\n      switch (command) {\n        case 'userInfo':\n          this.userForm = _objectSpread({}, this.userInfo);\n          this.userInfoVisible = true;\n          break;\n        case 'password':\n          this.passwordVisible = true;\n          break;\n        case 'logout':\n          this.$store.dispatch('logout').then(function () {\n            localStorage.clear();\n            _this2.$router.push('/login');\n          });\n          break;\n      }\n    },\n    handleAvatarSuccess: function handleAvatarSuccess(res) {\n      this.userForm.avatar = res.data;\n    },\n    beforeAvatarUpload: function beforeAvatarUpload(file) {\n      var isJPG = file.type === 'image/jpeg' || file.type === 'image/png';\n      var isLt2M = file.size / 1024 / 1024 < 2;\n      if (!isJPG) {\n        this.$message.error('上传头像图片只能是 JPG/PNG 格式!');\n      }\n      if (!isLt2M) {\n        this.$message.error('上传头像图片大小不能超过 2MB!');\n      }\n      return isJPG && isLt2M;\n    },\n    submitUserInfo: function submitUserInfo() {\n      var _this3 = this;\n      this.$refs.userForm.validate(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(valid) {\n          var res;\n          return _regeneratorRuntime().wrap(function _callee$(_context) {\n            while (1) switch (_context.prev = _context.next) {\n              case 0:\n                if (!valid) {\n                  _context.next = 18;\n                  break;\n                }\n                _context.prev = 1;\n                _context.next = 4;\n                return updateUserInfo(_this3.userForm);\n              case 4:\n                res = _context.sent;\n                if (!(res.code === 0)) {\n                  _context.next = 11;\n                  break;\n                }\n                // 新 Vuex 中的用户信息\n                _this3.$store.commit('SET_USER_INFO', _objectSpread(_objectSpread({}, _this3.userInfo), _this3.userForm));\n                _this3.$message.success('个人信息修改成功');\n                _this3.userInfoVisible = false;\n                _context.next = 12;\n                break;\n              case 11:\n                throw new Error(res.msg || '修改失败');\n              case 12:\n                _context.next = 18;\n                break;\n              case 14:\n                _context.prev = 14;\n                _context.t0 = _context[\"catch\"](1);\n                console.error('修改个人信息失败:', _context.t0);\n                _this3.$message.error(_context.t0.message || '修改失败，请重试');\n              case 18:\n              case \"end\":\n                return _context.stop();\n            }\n          }, _callee, null, [[1, 14]]);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    },\n    submitPassword: function submitPassword() {\n      var _this4 = this;\n      this.$refs.passwordForm.validate(/*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(valid) {\n          var params, res;\n          return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n            while (1) switch (_context2.prev = _context2.next) {\n              case 0:\n                if (!valid) {\n                  _context2.next = 21;\n                  break;\n                }\n                _context2.prev = 1;\n                params = {\n                  oldPassword: _this4.passwordForm.oldPassword,\n                  newPassword: _this4.passwordForm.newPassword\n                };\n                _context2.next = 5;\n                return updatePassword(params);\n              case 5:\n                res = _context2.sent;\n                if (!(res.code === 0)) {\n                  _context2.next = 14;\n                  break;\n                }\n                _this4.$message.success('密码修改成功，请重新登录');\n                // 修改密码成功后，退出登录\n                _context2.next = 10;\n                return _this4.$store.dispatch('logout');\n              case 10:\n                localStorage.clear();\n                _this4.$router.push('/login');\n                _context2.next = 15;\n                break;\n              case 14:\n                throw new Error(res.msg || '修改失败');\n              case 15:\n                _context2.next = 21;\n                break;\n              case 17:\n                _context2.prev = 17;\n                _context2.t0 = _context2[\"catch\"](1);\n                console.error('修改密码失败:', _context2.t0);\n                _this4.$message.error(_context2.t0.message || '修改密码失败，请重试');\n              case 21:\n              case \"end\":\n                return _context2.stop();\n            }\n          }, _callee2, null, [[1, 17]]);\n        }));\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }());\n    },\n    cancelPassword: function cancelPassword() {\n      this.passwordForm = {\n        oldPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      };\n      this.passwordVisible = false;\n    },\n    validateConfirmPassword: function validateConfirmPassword(rule, value, callback) {\n      if (value !== this.passwordForm.newPassword) {\n        callback(new Error('两次输入的密码不一致'));\n      } else {\n        callback();\n      }\n    },\n    // 获取菜单数据\n    getMenus: function getMenus() {\n      var _this5 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              _context3.next = 3;\n              return getNavMenus();\n            case 3:\n              res = _context3.sent;\n              if (res.code === 0) {\n                _this5.menus = res.data || [];\n              } else {\n                _this5.$message.error(res.msg || '获取菜单失败');\n              }\n              _context3.next = 11;\n              break;\n            case 7:\n              _context3.prev = 7;\n              _context3.t0 = _context3[\"catch\"](0);\n              console.error('获取菜单失败:', _context3.t0);\n              _this5.$message.error('获取菜单失败，请刷新重试');\n            case 11:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 7]]);\n      }))();\n    },\n    handleSelect: function handleSelect(index, indexPath) {\n      if (index === this.$route.path) {\n        return; // 如果是当前路由，直接返回\n      }\n      this.$router.push(index); // 不需要catch，因为已经在路由配置中处理了\n    }\n  }\n};", "map": {"version": 3, "names": ["mapGetters", "updateUserInfo", "updatePassword", "getNavMenus", "name", "data", "_this", "isCollapse", "userInfoVisible", "passwordVisible", "menus", "userForm", "avatar", "username", "nickname", "phone", "email", "userRules", "required", "message", "trigger", "pattern", "type", "passwordForm", "oldPassword", "newPassword", "confirmPassword", "passwordRules", "min", "validator", "rule", "value", "callback", "Error", "validateConfirmPassword", "loading", "computed", "_objectSpread", "created", "getMenus", "methods", "toggleSidebar", "handleCommand", "command", "_this2", "userInfo", "$store", "dispatch", "then", "localStorage", "clear", "$router", "push", "handleAvatarSuccess", "res", "beforeAvatarUpload", "file", "isJPG", "isLt2M", "size", "$message", "error", "submitUserInfo", "_this3", "$refs", "validate", "_ref", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "valid", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "commit", "success", "msg", "t0", "console", "stop", "_x", "apply", "arguments", "submitPassword", "_this4", "_ref2", "_callee2", "params", "_callee2$", "_context2", "_x2", "cancelPassword", "_this5", "_callee3", "_callee3$", "_context3", "handleSelect", "index", "indexPath", "$route", "path"], "sources": ["src/views/dashboard/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-wrapper\">\r\n    <!-- 添加路由加载进度条 -->\r\n    <el-progress \r\n      v-if=\"loading\"\r\n      :percentage=\"100\"\r\n      status=\"success\"\r\n      style=\"position: fixed; top: 0; left: 0; right: 0; z-index: 9999;\"\r\n    ></el-progress>\r\n    <!-- 侧边栏 -->\r\n    <div class=\"sidebar-container\" :class=\"{ 'is-collapse': isCollapse }\">\r\n      <div class=\"logo\">\r\n        <i class=\"el-icon-s-platform\" style=\"font-size: 32px;\"></i>\r\n        <span v-show=\"!isCollapse\">交易所管理后台系统</span>\r\n      </div>\r\n      \r\n      <el-menu\r\n        :default-active=\"$route.path\"\r\n        :collapse=\"isCollapse\"\r\n        router\r\n        @select=\"handleSelect\">\r\n        \r\n        <!-- 首页固定菜单 -->\r\n        <el-menu-item index=\"/dashboard\">\r\n          <i class=\"el-icon-s-home\"></i>\r\n          <span slot=\"title\">首页</span>\r\n        </el-menu-item>\r\n\r\n        <!-- 动态菜单 -->\r\n        <template v-for=\"menu in menus\">\r\n          <!-- 一级菜单 -->\r\n          <el-submenu \r\n            v-if=\"menu.children && menu.children.length > 0\" \r\n            :key=\"menu.id\" \r\n            :index=\"menu.path\">\r\n            <template slot=\"title\">\r\n              <i :class=\"'el-icon-' + menu.icon || 'el-icon-folder'\"></i>\r\n              <span>{{ menu.name }}</span>\r\n            </template>\r\n            \r\n            <!-- 二级菜单 -->\r\n            <template v-for=\"subMenu in menu.children\">\r\n              <el-submenu \r\n                v-if=\"subMenu.children && subMenu.children.length > 0\" \r\n                :key=\"subMenu.id\" \r\n                :index=\"'/' + subMenu.component\">\r\n                <template slot=\"title\">\r\n                  <i :class=\"subMenu.icon || 'el-icon-folder'\"></i>\r\n                  <span>{{ subMenu.name }}</span>\r\n                </template>\r\n                <el-menu-item \r\n                  v-for=\"child in subMenu.children\"\r\n                  :key=\"child.id\"\r\n                  :index=\"'/' + child.component\">\r\n                  {{ child.name }}\r\n                </el-menu-item>\r\n              </el-submenu>\r\n              \r\n              <el-menu-item \r\n                v-else \r\n                :key=\"subMenu.id\" \r\n                :index=\"'/' + subMenu.component\">\r\n                <i :class=\"subMenu.icon || 'el-icon-folder'\"></i>\r\n                <span>{{ subMenu.name }}</span>\r\n              </el-menu-item>\r\n            </template>\r\n          </el-submenu>\r\n          \r\n          <!-- 没有子菜单的一级菜单 -->\r\n          <el-menu-item \r\n            v-else \r\n            :key=\"menu.id\" \r\n            :index=\"menu.path\">\r\n            <i :class=\"menu.icon || 'el-icon-folder'\"></i>\r\n            <span slot=\"title\">{{ menu.name }}</span>\r\n          </el-menu-item>\r\n        </template>\r\n      </el-menu>\r\n    </div>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <div class=\"main-container\">\r\n      <!-- 顶部导航栏 -->\r\n      <div class=\"navbar\">\r\n        <div class=\"left\">\r\n          <i \r\n            :class=\"isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'\"\r\n            @click=\"toggleSidebar\"\r\n          ></i>\r\n          <el-breadcrumb separator=\"/\">\r\n            <el-breadcrumb-item :to=\"{ path: '/dashboard' }\">首页</el-breadcrumb-item>\r\n            <el-breadcrumb-item>{{ $route.meta.title }}</el-breadcrumb-item>\r\n          </el-breadcrumb>\r\n        </div>\r\n        <div class=\"right\">\r\n          <el-dropdown trigger=\"click\" @command=\"handleCommand\">\r\n            <span class=\"user-info\">\r\n              <div class=\"avatar\">\r\n                <i class=\"el-icon-user\"></i>\r\n              </div>\r\n              <span class=\"username\">{{ userInfo.username }}</span>\r\n              <!-- <i class=\"el-icon-caret-bottom\"></i> -->\r\n            </span>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <el-dropdown-item command=\"userInfo\">个人信息</el-dropdown-item>\r\n              <el-dropdown-item command=\"password\">修改密码</el-dropdown-item>\r\n              <el-dropdown-item divided command=\"logout\">退出登录</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 内容区域 -->\r\n      <div class=\"app-main\">\r\n        <router-view />\r\n      </div>\r\n\r\n      <!-- 个人信息对话框 -->\r\n      <el-dialog title=\"个人信息\" :visible.sync=\"userInfoVisible\" width=\"500px\">\r\n        <el-form :model=\"userForm\" :rules=\"userRules\" ref=\"userForm\" label-width=\"100px\">\r\n          <el-form-item label=\"用户名\">\r\n            <el-input v-model=\"userForm.username\" disabled></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"昵称\" prop=\"nickname\">\r\n            <el-input v-model=\"userForm.nickname\" placeholder=\"请输入昵称\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"手机号\" prop=\"phone\">\r\n            <el-input v-model=\"userForm.phone\" placeholder=\"请输入手机号\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"邮箱\" prop=\"email\">\r\n            <el-input v-model=\"userForm.email\" placeholder=\"请输入邮箱\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\">\r\n          <el-button @click=\"userInfoVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitUserInfo\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 修改密码对话框 -->\r\n      <el-dialog title=\"修改密码\" :visible.sync=\"passwordVisible\" width=\"500px\">\r\n        <el-form :model=\"passwordForm\" :rules=\"passwordRules\" ref=\"passwordForm\" label-width=\"100px\">\r\n          <el-form-item label=\"原密码\" prop=\"oldPassword\">\r\n            <el-input v-model=\"passwordForm.oldPassword\" type=\"password\" placeholder=\"请输入原密码\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"新密码\" prop=\"newPassword\">\r\n            <el-input v-model=\"passwordForm.newPassword\" type=\"password\" placeholder=\"请输入新密码\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\r\n            <el-input v-model=\"passwordForm.confirmPassword\" type=\"password\" placeholder=\"请再次输入新密码\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\">\r\n          <el-button @click=\"cancelPassword\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitPassword\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport { updateUserInfo, updatePassword } from '@/api/auth'\r\nimport { getNavMenus } from '@/api/menu'  // 导入获取菜单的API\r\n\r\nexport default {\r\n  name: 'Layout',\r\n  data() {\r\n    return {\r\n      isCollapse: false,\r\n      userInfoVisible: false,\r\n      passwordVisible: false,\r\n      menus: [], // 存储菜单数据\r\n      userForm: {\r\n        avatar: '',\r\n        username: '',\r\n        nickname: '',\r\n        phone: '',\r\n        email: ''\r\n      },\r\n      userRules: {\r\n        nickname: [\r\n          { required: true, message: '请输入昵称', trigger: 'blur' }\r\n        ],\r\n        phone: [\r\n          { required: true, message: '请输入手机号', trigger: 'blur' },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n        email: [\r\n          { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n        ]\r\n      },\r\n      passwordForm: {\r\n        oldPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: ''\r\n      },\r\n      passwordRules: {\r\n        oldPassword: [\r\n          { required: true, message: '请输原密码', trigger: 'blur' },\r\n          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }\r\n        ],\r\n        newPassword: [\r\n          { required: true, message: '请输入新密码', trigger: 'blur' },\r\n          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' },\r\n          { validator: (rule, value, callback) => {\r\n            if (value === this.passwordForm.oldPassword) {\r\n              callback(new Error('新密不能与原密码相同'))\r\n            } else {\r\n              callback()\r\n            }\r\n          }, trigger: 'blur' }\r\n        ],\r\n        confirmPassword: [\r\n          { required: true, message: '请再次输入新密码', trigger: 'blur' },\r\n          { validator: this.validateConfirmPassword, trigger: 'blur' }\r\n        ]\r\n      },\r\n      loading: false,\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters(['userInfo'])\r\n  },\r\n  created() {\r\n    // 组件创建时获取菜单数据\r\n    this.getMenus()\r\n  },\r\n  methods: {\r\n    toggleSidebar() {\r\n      this.isCollapse = !this.isCollapse\r\n    },\r\n    handleCommand(command) {\r\n      switch (command) {\r\n        case 'userInfo':\r\n          this.userForm = { ...this.userInfo }\r\n          this.userInfoVisible = true\r\n          break\r\n        case 'password':\r\n          this.passwordVisible = true\r\n          break\r\n        case 'logout':\r\n          this.$store.dispatch('logout').then(() => {\r\n            localStorage.clear()\r\n            this.$router.push('/login')\r\n          })\r\n          break\r\n      }\r\n    },\r\n    handleAvatarSuccess(res) {\r\n      this.userForm.avatar = res.data\r\n    },\r\n    beforeAvatarUpload(file) {\r\n      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'\r\n      const isLt2M = file.size / 1024 / 1024 < 2\r\n\r\n      if (!isJPG) {\r\n        this.$message.error('上传头像图片只能是 JPG/PNG 格式!')\r\n      }\r\n      if (!isLt2M) {\r\n        this.$message.error('上传头像图片大小不能超过 2MB!')\r\n      }\r\n      return isJPG && isLt2M\r\n    },\r\n    submitUserInfo() {\r\n      this.$refs.userForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            // 调用更新用户信息的接口\r\n            const res = await updateUserInfo(this.userForm)\r\n            if (res.code === 0) {\r\n              // 新 Vuex 中的用户信息\r\n              this.$store.commit('SET_USER_INFO', {\r\n                ...this.userInfo,  // 保留原有信息\r\n                ...this.userForm   // 更新修改的字段\r\n              })\r\n              this.$message.success('个人信息修改成功')\r\n              this.userInfoVisible = false\r\n            } else {\r\n              throw new Error(res.msg || '修改失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('修改个人信息失败:', error)\r\n            this.$message.error(error.message || '修改失败，请重试')\r\n          }\r\n        }\r\n      })\r\n    },\r\n    submitPassword() {\r\n      this.$refs.passwordForm.validate(async valid => {\r\n        if (valid) {\r\n          try {\r\n            const params = {\r\n              oldPassword: this.passwordForm.oldPassword,\r\n              newPassword: this.passwordForm.newPassword\r\n            }\r\n            \r\n            const res = await updatePassword(params)\r\n            \r\n            if (res.code === 0) {\r\n              this.$message.success('密码修改成功，请重新登录')\r\n              // 修改密码成功后，退出登录\r\n              await this.$store.dispatch('logout')\r\n              localStorage.clear()\r\n              this.$router.push('/login')\r\n            } else {\r\n              throw new Error(res.msg || '修改失败')\r\n            }\r\n          } catch (error) {\r\n            console.error('修改密码失败:', error)\r\n            this.$message.error(error.message || '修改密码失败，请重试')\r\n          }\r\n        }\r\n      })\r\n    },\r\n    cancelPassword() {\r\n          this.passwordForm = {\r\n            oldPassword: '',\r\n            newPassword: '',\r\n            confirmPassword: ''\r\n          }\r\n      this.passwordVisible = false\r\n    },\r\n    validateConfirmPassword(rule, value, callback) {\r\n      if (value !== this.passwordForm.newPassword) {\r\n        callback(new Error('两次输入的密码不一致'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    // 获取菜单数据\r\n    async getMenus() {\r\n      try {\r\n        const res = await getNavMenus()\r\n        if (res.code === 0) {\r\n          this.menus = res.data || []\r\n        } else {\r\n          this.$message.error(res.msg || '获取菜单失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取菜单失败:', error)\r\n        this.$message.error('获取菜单失败，请刷新重试')\r\n      }\r\n    },\r\n    handleSelect(index, indexPath) {\r\n      if (index === this.$route.path) {\r\n        return  // 如果是当前路由，直接返回\r\n      }\r\n      this.$router.push(index)  // 不需要catch，因为已经在路由配置中处理了\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-wrapper {\r\n  height: 100%;\r\n  display: flex;\r\n\r\n  .sidebar-container {\r\n    width: 260px;\r\n    height: 100%;\r\n    background: #181818;\r\n    transition: width 0.3s;\r\n    display: flex;\r\n    flex-direction: column;\r\n    border-right: 1.5px solid #FFD700;\r\n\r\n    &.is-collapse {\r\n      width: 64px;\r\n    }\r\n\r\n    .logo {\r\n      height: 60px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 0 20px;\r\n      color: #FFD700;\r\n      background: #232323;\r\n      flex-shrink: 0;\r\n      border-bottom: 1.5px solid #FFD70033;\r\n      font-weight: bold;\r\n      font-size: 20px;\r\n      letter-spacing: 1px;\r\n      i {\r\n        font-size: 32px;\r\n        color: #FFD700;\r\n        margin-right: 12px;\r\n      }\r\n      span {\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n        white-space: nowrap;\r\n        color: #FFD700;\r\n      }\r\n    }\r\n\r\n    .el-menu {\r\n      border: none;\r\n      flex: 1;\r\n      overflow-y: auto;\r\n      background: #181818 !important;\r\n      color: #fff !important;\r\n      .el-menu-item, .el-submenu__title {\r\n        color: #fff !important;\r\n        font-weight: bold;\r\n        font-size: 17px;\r\n        text-shadow: 0 1px 6px rgba(255,255,255,0.12);\r\n        opacity: 1 !important;\r\n        i {\r\n          color: #fff !important;\r\n          opacity: 1 !important;\r\n        }\r\n        span {\r\n          color: #fff !important;\r\n        }\r\n      }\r\n      .el-menu-item.is-active,\r\n      .el-submenu.is-opened > .el-submenu__title {\r\n        color: #FFD700 !important;\r\n        font-weight: bold;\r\n        i {\r\n          color: #FFD700 !important;\r\n        }\r\n        span {\r\n          color: #FFD700 !important;\r\n        }\r\n      }\r\n      .el-menu-item {\r\n        background: transparent !important;\r\n        &:hover {\r\n          background: #232323 !important;\r\n          color: #FFD700 !important;\r\n        }\r\n      }\r\n      .el-submenu__title {\r\n        &:hover {\r\n          color: #FFD700 !important;\r\n        }\r\n      }\r\n      .el-menu-item, .el-submenu__title {\r\n        border-radius: 6px;\r\n        margin: 2px 8px;\r\n      }\r\n      /* 自定义滚动条样式 */\r\n      &::-webkit-scrollbar {\r\n        width: 6px;\r\n      }\r\n      &::-webkit-scrollbar-thumb {\r\n        background: #FFD70033;\r\n        border-radius: 3px;\r\n      }\r\n      &::-webkit-scrollbar-track {\r\n        background: transparent;\r\n      }\r\n    }\r\n    // 强化子菜单背景色\r\n    ::v-deep .el-menu--popup {\r\n      background: #232323 !important;\r\n      color: #fff !important;\r\n      border-radius: 8px !important;\r\n      box-shadow: 0 4px 24px #000a !important;\r\n      .el-menu-item {\r\n        background: #232323 !important;\r\n        color: #fff !important;\r\n        border-radius: 6px !important;\r\n        margin: 2px 8px !important;\r\n        &:hover {\r\n          background: linear-gradient(90deg, #FFD70033 0%, #bfa14033 100%) !important;\r\n          color: #FFD700 !important;\r\n        }\r\n        &.is-active {\r\n          background: linear-gradient(90deg, #FFD70033 0%, #bfa14033 100%) !important;\r\n          color: #FFD700 !important;\r\n        }\r\n      }\r\n      .el-submenu__title {\r\n        background: #232323 !important;\r\n        color: #FFD700 !important;\r\n        border-radius: 6px !important;\r\n        margin: 2px 8px !important;\r\n        &:hover {\r\n          color: #FFD700 !important;\r\n        }\r\n      }\r\n    }\r\n    // 覆盖嵌入式二级菜单（侧边栏内嵌el-menu）\r\n    ::v-deep .el-menu .el-menu {\r\n      background: #232323 !important;\r\n    }\r\n    ::v-deep .el-menu .el-menu .el-menu-item {\r\n      background: #232323 !important;\r\n      color: #fff !important;\r\n      font-weight: normal !important;\r\n      font-size: 15px !important;\r\n    }\r\n    ::v-deep .el-menu .el-menu .el-menu-item.is-active,\r\n    ::v-deep .el-menu .el-menu .el-menu-item:hover {\r\n      background: linear-gradient(90deg, #FFD70033 0%, #bfa14033 100%) !important;\r\n      color: #FFD700 !important;\r\n    }\r\n  }\r\n\r\n  .main-container {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n\r\n    .navbar {\r\n      height: 60px;\r\n      background: #181818;\r\n      box-shadow: 0 1px 8px #FFD70022;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 0 20px;\r\n      border-bottom: 1.5px solid #FFD70033;\r\n      .left {\r\n        display: flex;\r\n        align-items: center;\r\n        i {\r\n          font-size: 20px;\r\n          cursor: pointer;\r\n          margin-right: 20px;\r\n          color: #FFD700;\r\n          &:hover {\r\n            color: #ffe066;\r\n          }\r\n        }\r\n        .el-breadcrumb {\r\n          .el-breadcrumb__item {\r\n            .el-breadcrumb__inner {\r\n              color: #FFD700;\r\n              font-weight: bold;\r\n            }\r\n            &:last-child .el-breadcrumb__inner {\r\n              color: #fff;\r\n              font-weight: normal;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .right {\r\n        .user-info {\r\n          display: flex;\r\n          align-items: center;\r\n          cursor: pointer;\r\n          .avatar {\r\n            width: 32px;\r\n            height: 32px;\r\n            border-radius: 50%;\r\n            margin-right: 8px;\r\n            background: #232323;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            border: 1.5px solid #FFD700;\r\n            i {\r\n              font-size: 20px;\r\n              color: #FFD700;\r\n            }\r\n          }\r\n          .username {\r\n            margin: 0 8px;\r\n            font-size: 15px;\r\n            color: #FFD700;\r\n            font-weight: bold;\r\n          }\r\n          i {\r\n            margin-left: 4px;\r\n            font-size: 12px;\r\n            color: #FFD700;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .app-main {\r\n      flex: 1;\r\n      padding: 20px;\r\n      overflow: auto;\r\n      background: linear-gradient(135deg, #181818 0%, #232323 100%);\r\n      color: #fff;\r\n      border-top: none;\r\n      /* 自定义滚动条样式 */\r\n      &::-webkit-scrollbar {\r\n        width: 8px;\r\n      }\r\n      &::-webkit-scrollbar-thumb {\r\n        background: #FFD70033;\r\n        border-radius: 4px;\r\n      }\r\n      &::-webkit-scrollbar-track {\r\n        background: transparent;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 覆盖 Element UI 弹窗样式\r\n::v-deep .el-dialog {\r\n  background: #181818 !important;\r\n  border-radius: 12px;\r\n  .el-dialog__header {\r\n    color: #FFD700;\r\n    border-bottom: 1px solid #FFD70033;\r\n  }\r\n  .el-dialog__footer {\r\n    .el-button--primary {\r\n      background: linear-gradient(90deg, #FFD700 0%, #bfa140 100%);\r\n      color: #181818;\r\n      border: none;\r\n      font-weight: bold;\r\n      &:hover {\r\n        background: linear-gradient(90deg, #ffe066 0%, #FFD700 100%);\r\n        color: #000;\r\n      }\r\n    }\r\n  }\r\n  .el-form-item__label {\r\n    color: #FFD700;\r\n  }\r\n  .el-input__inner {\r\n    background: #232323;\r\n    border: 1.5px solid #FFD700;\r\n    color: #fff;\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n// 黑金主题下拉菜单全局样式\r\n.el-dropdown-menu {\r\n  background: #232323 !important;\r\n  border-radius: 8px !important;\r\n  box-shadow: 0 4px 24px #000a !important;\r\n}\r\n.el-dropdown-menu__item {\r\n  color: #fff !important;\r\n  font-size: 15px;\r\n}\r\n.el-dropdown-menu__item:hover, .el-dropdown-menu__item.selected {\r\n  background: #181818 !important;\r\n  color: #FFD700 !important;\r\n}\r\n.el-dropdown-menu__item--divided {\r\n  border-top: 1px solid #FFD70033 !important;\r\n  background: none !important;\r\n  box-shadow: none !important;\r\n  margin-top: 4px !important;\r\n  padding-top: 8px !important;\r\n}\r\n</style> "], "mappings": ";;;;;;AAkKA,SAAAA,UAAA;AACA,SAAAC,cAAA,EAAAC,cAAA;AACA,SAAAC,WAAA;;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,UAAA;MACAC,eAAA;MACAC,eAAA;MACAC,KAAA;MAAA;MACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,QAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACAC,SAAA;QACAH,QAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,KAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,OAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,KAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAE,IAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAG,YAAA;QACAC,WAAA;QACAC,WAAA;QACAC,eAAA;MACA;MACAC,aAAA;QACAH,WAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,WAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAQ,GAAA;UAAAT,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAS,SAAA,WAAAA,UAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA,IAAAD,KAAA,KAAAzB,KAAA,CAAAiB,YAAA,CAAAC,WAAA;cACAQ,QAAA,KAAAC,KAAA;YACA;cACAD,QAAA;YACA;UACA;UAAAZ,OAAA;QAAA,EACA;QACAM,eAAA,GACA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAS,SAAA,OAAAK,uBAAA;UAAAd,OAAA;QAAA;MAEA;MACAe,OAAA;IACA;EACA;EACAC,QAAA,EAAAC,aAAA,KACArC,UAAA,eACA;EACAsC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAlC,UAAA,SAAAA,UAAA;IACA;IACAmC,aAAA,WAAAA,cAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,QAAAD,OAAA;QACA;UACA,KAAAhC,QAAA,GAAA0B,aAAA,UAAAQ,QAAA;UACA,KAAArC,eAAA;UACA;QACA;UACA,KAAAC,eAAA;UACA;QACA;UACA,KAAAqC,MAAA,CAAAC,QAAA,WAAAC,IAAA;YACAC,YAAA,CAAAC,KAAA;YACAN,MAAA,CAAAO,OAAA,CAAAC,IAAA;UACA;UACA;MACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,GAAA;MACA,KAAA3C,QAAA,CAAAC,MAAA,GAAA0C,GAAA,CAAAjD,IAAA;IACA;IACAkD,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAC,KAAA,GAAAD,IAAA,CAAAlC,IAAA,qBAAAkC,IAAA,CAAAlC,IAAA;MACA,IAAAoC,MAAA,GAAAF,IAAA,CAAAG,IAAA;MAEA,KAAAF,KAAA;QACA,KAAAG,QAAA,CAAAC,KAAA;MACA;MACA,KAAAH,MAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;MACA;MACA,OAAAJ,KAAA,IAAAC,MAAA;IACA;IACAI,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAArD,QAAA,CAAAsD,QAAA;QAAA,IAAAC,IAAA,GAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAAC,KAAA;UAAA,IAAAjB,GAAA;UAAA,OAAAc,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;YAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;cAAA;gBAAA,KACAL,KAAA;kBAAAG,QAAA,CAAAE,IAAA;kBAAA;gBAAA;gBAAAF,QAAA,CAAAC,IAAA;gBAAAD,QAAA,CAAAE,IAAA;gBAAA,OAGA3E,cAAA,CAAA8D,MAAA,CAAApD,QAAA;cAAA;gBAAA2C,GAAA,GAAAoB,QAAA,CAAAG,IAAA;gBAAA,MACAvB,GAAA,CAAAwB,IAAA;kBAAAJ,QAAA,CAAAE,IAAA;kBAAA;gBAAA;gBACA;gBACAb,MAAA,CAAAjB,MAAA,CAAAiC,MAAA,kBAAA1C,aAAA,CAAAA,aAAA,KACA0B,MAAA,CAAAlB,QAAA,GACAkB,MAAA,CAAApD,QAAA,CACA;gBACAoD,MAAA,CAAAH,QAAA,CAAAoB,OAAA;gBACAjB,MAAA,CAAAvD,eAAA;gBAAAkE,QAAA,CAAAE,IAAA;gBAAA;cAAA;gBAAA,MAEA,IAAA3C,KAAA,CAAAqB,GAAA,CAAA2B,GAAA;cAAA;gBAAAP,QAAA,CAAAE,IAAA;gBAAA;cAAA;gBAAAF,QAAA,CAAAC,IAAA;gBAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;gBAGAS,OAAA,CAAAtB,KAAA,cAAAa,QAAA,CAAAQ,EAAA;gBACAnB,MAAA,CAAAH,QAAA,CAAAC,KAAA,CAAAa,QAAA,CAAAQ,EAAA,CAAA/D,OAAA;cAAA;cAAA;gBAAA,OAAAuD,QAAA,CAAAU,IAAA;YAAA;UAAA,GAAAd,OAAA;QAAA,CAGA;QAAA,iBAAAe,EAAA;UAAA,OAAAnB,IAAA,CAAAoB,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAzB,KAAA,CAAAzC,YAAA,CAAA0C,QAAA;QAAA,IAAAyB,KAAA,GAAAvB,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAsB,SAAApB,KAAA;UAAA,IAAAqB,MAAA,EAAAtC,GAAA;UAAA,OAAAc,mBAAA,GAAAI,IAAA,UAAAqB,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAnB,IAAA,GAAAmB,SAAA,CAAAlB,IAAA;cAAA;gBAAA,KACAL,KAAA;kBAAAuB,SAAA,CAAAlB,IAAA;kBAAA;gBAAA;gBAAAkB,SAAA,CAAAnB,IAAA;gBAEAiB,MAAA;kBACApE,WAAA,EAAAiE,MAAA,CAAAlE,YAAA,CAAAC,WAAA;kBACAC,WAAA,EAAAgE,MAAA,CAAAlE,YAAA,CAAAE;gBACA;gBAAAqE,SAAA,CAAAlB,IAAA;gBAAA,OAEA1E,cAAA,CAAA0F,MAAA;cAAA;gBAAAtC,GAAA,GAAAwC,SAAA,CAAAjB,IAAA;gBAAA,MAEAvB,GAAA,CAAAwB,IAAA;kBAAAgB,SAAA,CAAAlB,IAAA;kBAAA;gBAAA;gBACAa,MAAA,CAAA7B,QAAA,CAAAoB,OAAA;gBACA;gBAAAc,SAAA,CAAAlB,IAAA;gBAAA,OACAa,MAAA,CAAA3C,MAAA,CAAAC,QAAA;cAAA;gBACAE,YAAA,CAAAC,KAAA;gBACAuC,MAAA,CAAAtC,OAAA,CAAAC,IAAA;gBAAA0C,SAAA,CAAAlB,IAAA;gBAAA;cAAA;gBAAA,MAEA,IAAA3C,KAAA,CAAAqB,GAAA,CAAA2B,GAAA;cAAA;gBAAAa,SAAA,CAAAlB,IAAA;gBAAA;cAAA;gBAAAkB,SAAA,CAAAnB,IAAA;gBAAAmB,SAAA,CAAAZ,EAAA,GAAAY,SAAA;gBAGAX,OAAA,CAAAtB,KAAA,YAAAiC,SAAA,CAAAZ,EAAA;gBACAO,MAAA,CAAA7B,QAAA,CAAAC,KAAA,CAAAiC,SAAA,CAAAZ,EAAA,CAAA/D,OAAA;cAAA;cAAA;gBAAA,OAAA2E,SAAA,CAAAV,IAAA;YAAA;UAAA,GAAAO,QAAA;QAAA,CAGA;QAAA,iBAAAI,GAAA;UAAA,OAAAL,KAAA,CAAAJ,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IACAS,cAAA,WAAAA,eAAA;MACA,KAAAzE,YAAA;QACAC,WAAA;QACAC,WAAA;QACAC,eAAA;MACA;MACA,KAAAjB,eAAA;IACA;IACAyB,uBAAA,WAAAA,wBAAAJ,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAD,KAAA,UAAAR,YAAA,CAAAE,WAAA;QACAO,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IACA;IACAO,QAAA,WAAAA,SAAA;MAAA,IAAA0D,MAAA;MAAA,OAAA9B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAA6B,SAAA;QAAA,IAAA5C,GAAA;QAAA,OAAAc,mBAAA,GAAAI,IAAA,UAAA2B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzB,IAAA,GAAAyB,SAAA,CAAAxB,IAAA;YAAA;cAAAwB,SAAA,CAAAzB,IAAA;cAAAyB,SAAA,CAAAxB,IAAA;cAAA,OAEAzE,WAAA;YAAA;cAAAmD,GAAA,GAAA8C,SAAA,CAAAvB,IAAA;cACA,IAAAvB,GAAA,CAAAwB,IAAA;gBACAmB,MAAA,CAAAvF,KAAA,GAAA4C,GAAA,CAAAjD,IAAA;cACA;gBACA4F,MAAA,CAAArC,QAAA,CAAAC,KAAA,CAAAP,GAAA,CAAA2B,GAAA;cACA;cAAAmB,SAAA,CAAAxB,IAAA;cAAA;YAAA;cAAAwB,SAAA,CAAAzB,IAAA;cAAAyB,SAAA,CAAAlB,EAAA,GAAAkB,SAAA;cAEAjB,OAAA,CAAAtB,KAAA,YAAAuC,SAAA,CAAAlB,EAAA;cACAe,MAAA,CAAArC,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAuC,SAAA,CAAAhB,IAAA;UAAA;QAAA,GAAAc,QAAA;MAAA;IAEA;IACAG,YAAA,WAAAA,aAAAC,KAAA,EAAAC,SAAA;MACA,IAAAD,KAAA,UAAAE,MAAA,CAAAC,IAAA;QACA;MACA;MACA,KAAAtD,OAAA,CAAAC,IAAA,CAAAkD,KAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}