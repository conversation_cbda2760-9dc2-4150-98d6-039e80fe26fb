{"ast": null, "code": "import \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.last-index-of.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.sub.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.filter.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parseClassType } from './clazz.js';\n/**\n * Usage of query:\n * `chart.on('click', query, handler);`\n * The `query` can be:\n * + The component type query string, only `mainType` or `mainType.subType`,\n *   like: 'xAxis', 'series', 'xAxis.category' or 'series.line'.\n * + The component query object, like:\n *   `{seriesIndex: 2}`, `{seriesName: 'xx'}`, `{seriesId: 'some'}`,\n *   `{xAxisIndex: 2}`, `{xAxisName: 'xx'}`, `{xAxisId: 'some'}`.\n * + The data query object, like:\n *   `{dataIndex: 123}`, `{dataType: 'link'}`, `{name: 'some'}`.\n * + The other query object (cmponent customized query), like:\n *   `{element: 'some'}` (only available in custom series).\n *\n * Caveat: If a prop in the `query` object is `null/undefined`, it is the\n * same as there is no such prop in the `query` object.\n */\nvar ECEventProcessor = /** @class */function () {\n  function ECEventProcessor() {}\n  ECEventProcessor.prototype.normalizeQuery = function (query) {\n    var cptQuery = {};\n    var dataQuery = {};\n    var otherQuery = {};\n    // `query` is `mainType` or `mainType.subType` of component.\n    if (zrUtil.isString(query)) {\n      var condCptType = parseClassType(query);\n      // `.main` and `.sub` may be ''.\n      cptQuery.mainType = condCptType.main || null;\n      cptQuery.subType = condCptType.sub || null;\n    }\n    // `query` is an object, convert to {mainType, index, name, id}.\n    else {\n      // `xxxIndex`, `xxxName`, `xxxId`, `name`, `dataIndex`, `dataType` is reserved,\n      // can not be used in `compomentModel.filterForExposedEvent`.\n      var suffixes_1 = ['Index', 'Name', 'Id'];\n      var dataKeys_1 = {\n        name: 1,\n        dataIndex: 1,\n        dataType: 1\n      };\n      zrUtil.each(query, function (val, key) {\n        var reserved = false;\n        for (var i = 0; i < suffixes_1.length; i++) {\n          var propSuffix = suffixes_1[i];\n          var suffixPos = key.lastIndexOf(propSuffix);\n          if (suffixPos > 0 && suffixPos === key.length - propSuffix.length) {\n            var mainType = key.slice(0, suffixPos);\n            // Consider `dataIndex`.\n            if (mainType !== 'data') {\n              cptQuery.mainType = mainType;\n              cptQuery[propSuffix.toLowerCase()] = val;\n              reserved = true;\n            }\n          }\n        }\n        if (dataKeys_1.hasOwnProperty(key)) {\n          dataQuery[key] = val;\n          reserved = true;\n        }\n        if (!reserved) {\n          otherQuery[key] = val;\n        }\n      });\n    }\n    return {\n      cptQuery: cptQuery,\n      dataQuery: dataQuery,\n      otherQuery: otherQuery\n    };\n  };\n  ECEventProcessor.prototype.filter = function (eventType, query) {\n    // They should be assigned before each trigger call.\n    var eventInfo = this.eventInfo;\n    if (!eventInfo) {\n      return true;\n    }\n    var targetEl = eventInfo.targetEl;\n    var packedEvent = eventInfo.packedEvent;\n    var model = eventInfo.model;\n    var view = eventInfo.view;\n    // For event like 'globalout'.\n    if (!model || !view) {\n      return true;\n    }\n    var cptQuery = query.cptQuery;\n    var dataQuery = query.dataQuery;\n    return check(cptQuery, model, 'mainType') && check(cptQuery, model, 'subType') && check(cptQuery, model, 'index', 'componentIndex') && check(cptQuery, model, 'name') && check(cptQuery, model, 'id') && check(dataQuery, packedEvent, 'name') && check(dataQuery, packedEvent, 'dataIndex') && check(dataQuery, packedEvent, 'dataType') && (!view.filterForExposedEvent || view.filterForExposedEvent(eventType, query.otherQuery, targetEl, packedEvent));\n    function check(query, host, prop, propOnHost) {\n      return query[prop] == null || host[propOnHost || prop] === query[prop];\n    }\n  };\n  ECEventProcessor.prototype.afterTrigger = function () {\n    // Make sure the eventInfo won't be used in next trigger.\n    this.eventInfo = null;\n  };\n  return ECEventProcessor;\n}();\nexport { ECEventProcessor };\n;", "map": {"version": 3, "names": ["zrUtil", "parseClassType", "ECEventProcessor", "prototype", "normalizeQuery", "query", "cpt<PERSON><PERSON><PERSON>", "dataQuery", "other<PERSON><PERSON>y", "isString", "condCptType", "mainType", "main", "subType", "sub", "suffixes_1", "dataKeys_1", "name", "dataIndex", "dataType", "each", "val", "key", "reserved", "i", "length", "propSuffix", "suffixPos", "lastIndexOf", "slice", "toLowerCase", "hasOwnProperty", "filter", "eventType", "eventInfo", "targetEl", "packedEvent", "model", "view", "check", "filterForExposedEvent", "host", "prop", "propOnHost", "afterTrigger"], "sources": ["F:/常规项目/华通云/adminweb/node_modules/echarts/lib/util/ECEventProcessor.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { parseClassType } from './clazz.js';\n/**\n * Usage of query:\n * `chart.on('click', query, handler);`\n * The `query` can be:\n * + The component type query string, only `mainType` or `mainType.subType`,\n *   like: 'xAxis', 'series', 'xAxis.category' or 'series.line'.\n * + The component query object, like:\n *   `{seriesIndex: 2}`, `{seriesName: 'xx'}`, `{seriesId: 'some'}`,\n *   `{xAxisIndex: 2}`, `{xAxisName: 'xx'}`, `{xAxisId: 'some'}`.\n * + The data query object, like:\n *   `{dataIndex: 123}`, `{dataType: 'link'}`, `{name: 'some'}`.\n * + The other query object (cmponent customized query), like:\n *   `{element: 'some'}` (only available in custom series).\n *\n * Caveat: If a prop in the `query` object is `null/undefined`, it is the\n * same as there is no such prop in the `query` object.\n */\nvar ECEventProcessor = /** @class */function () {\n  function ECEventProcessor() {}\n  ECEventProcessor.prototype.normalizeQuery = function (query) {\n    var cptQuery = {};\n    var dataQuery = {};\n    var otherQuery = {};\n    // `query` is `mainType` or `mainType.subType` of component.\n    if (zrUtil.isString(query)) {\n      var condCptType = parseClassType(query);\n      // `.main` and `.sub` may be ''.\n      cptQuery.mainType = condCptType.main || null;\n      cptQuery.subType = condCptType.sub || null;\n    }\n    // `query` is an object, convert to {mainType, index, name, id}.\n    else {\n      // `xxxIndex`, `xxxName`, `xxxId`, `name`, `dataIndex`, `dataType` is reserved,\n      // can not be used in `compomentModel.filterForExposedEvent`.\n      var suffixes_1 = ['Index', 'Name', 'Id'];\n      var dataKeys_1 = {\n        name: 1,\n        dataIndex: 1,\n        dataType: 1\n      };\n      zrUtil.each(query, function (val, key) {\n        var reserved = false;\n        for (var i = 0; i < suffixes_1.length; i++) {\n          var propSuffix = suffixes_1[i];\n          var suffixPos = key.lastIndexOf(propSuffix);\n          if (suffixPos > 0 && suffixPos === key.length - propSuffix.length) {\n            var mainType = key.slice(0, suffixPos);\n            // Consider `dataIndex`.\n            if (mainType !== 'data') {\n              cptQuery.mainType = mainType;\n              cptQuery[propSuffix.toLowerCase()] = val;\n              reserved = true;\n            }\n          }\n        }\n        if (dataKeys_1.hasOwnProperty(key)) {\n          dataQuery[key] = val;\n          reserved = true;\n        }\n        if (!reserved) {\n          otherQuery[key] = val;\n        }\n      });\n    }\n    return {\n      cptQuery: cptQuery,\n      dataQuery: dataQuery,\n      otherQuery: otherQuery\n    };\n  };\n  ECEventProcessor.prototype.filter = function (eventType, query) {\n    // They should be assigned before each trigger call.\n    var eventInfo = this.eventInfo;\n    if (!eventInfo) {\n      return true;\n    }\n    var targetEl = eventInfo.targetEl;\n    var packedEvent = eventInfo.packedEvent;\n    var model = eventInfo.model;\n    var view = eventInfo.view;\n    // For event like 'globalout'.\n    if (!model || !view) {\n      return true;\n    }\n    var cptQuery = query.cptQuery;\n    var dataQuery = query.dataQuery;\n    return check(cptQuery, model, 'mainType') && check(cptQuery, model, 'subType') && check(cptQuery, model, 'index', 'componentIndex') && check(cptQuery, model, 'name') && check(cptQuery, model, 'id') && check(dataQuery, packedEvent, 'name') && check(dataQuery, packedEvent, 'dataIndex') && check(dataQuery, packedEvent, 'dataType') && (!view.filterForExposedEvent || view.filterForExposedEvent(eventType, query.otherQuery, targetEl, packedEvent));\n    function check(query, host, prop, propOnHost) {\n      return query[prop] == null || host[propOnHost || prop] === query[prop];\n    }\n  };\n  ECEventProcessor.prototype.afterTrigger = function () {\n    // Make sure the eventInfo won't be used in next trigger.\n    this.eventInfo = null;\n  };\n  return ECEventProcessor;\n}();\nexport { ECEventProcessor };\n;"], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,cAAc,QAAQ,YAAY;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,aAAa,YAAY;EAC9C,SAASA,gBAAgBA,CAAA,EAAG,CAAC;EAC7BA,gBAAgB,CAACC,SAAS,CAACC,cAAc,GAAG,UAAUC,KAAK,EAAE;IAC3D,IAAIC,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIC,UAAU,GAAG,CAAC,CAAC;IACnB;IACA,IAAIR,MAAM,CAACS,QAAQ,CAACJ,KAAK,CAAC,EAAE;MAC1B,IAAIK,WAAW,GAAGT,cAAc,CAACI,KAAK,CAAC;MACvC;MACAC,QAAQ,CAACK,QAAQ,GAAGD,WAAW,CAACE,IAAI,IAAI,IAAI;MAC5CN,QAAQ,CAACO,OAAO,GAAGH,WAAW,CAACI,GAAG,IAAI,IAAI;IAC5C;IACA;IAAA,KACK;MACH;MACA;MACA,IAAIC,UAAU,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC;MACxC,IAAIC,UAAU,GAAG;QACfC,IAAI,EAAE,CAAC;QACPC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE;MACZ,CAAC;MACDnB,MAAM,CAACoB,IAAI,CAACf,KAAK,EAAE,UAAUgB,GAAG,EAAEC,GAAG,EAAE;QACrC,IAAIC,QAAQ,GAAG,KAAK;QACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,UAAU,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;UAC1C,IAAIE,UAAU,GAAGX,UAAU,CAACS,CAAC,CAAC;UAC9B,IAAIG,SAAS,GAAGL,GAAG,CAACM,WAAW,CAACF,UAAU,CAAC;UAC3C,IAAIC,SAAS,GAAG,CAAC,IAAIA,SAAS,KAAKL,GAAG,CAACG,MAAM,GAAGC,UAAU,CAACD,MAAM,EAAE;YACjE,IAAId,QAAQ,GAAGW,GAAG,CAACO,KAAK,CAAC,CAAC,EAAEF,SAAS,CAAC;YACtC;YACA,IAAIhB,QAAQ,KAAK,MAAM,EAAE;cACvBL,QAAQ,CAACK,QAAQ,GAAGA,QAAQ;cAC5BL,QAAQ,CAACoB,UAAU,CAACI,WAAW,CAAC,CAAC,CAAC,GAAGT,GAAG;cACxCE,QAAQ,GAAG,IAAI;YACjB;UACF;QACF;QACA,IAAIP,UAAU,CAACe,cAAc,CAACT,GAAG,CAAC,EAAE;UAClCf,SAAS,CAACe,GAAG,CAAC,GAAGD,GAAG;UACpBE,QAAQ,GAAG,IAAI;QACjB;QACA,IAAI,CAACA,QAAQ,EAAE;UACbf,UAAU,CAACc,GAAG,CAAC,GAAGD,GAAG;QACvB;MACF,CAAC,CAAC;IACJ;IACA,OAAO;MACLf,QAAQ,EAAEA,QAAQ;MAClBC,SAAS,EAAEA,SAAS;MACpBC,UAAU,EAAEA;IACd,CAAC;EACH,CAAC;EACDN,gBAAgB,CAACC,SAAS,CAAC6B,MAAM,GAAG,UAAUC,SAAS,EAAE5B,KAAK,EAAE;IAC9D;IACA,IAAI6B,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,IAAI;IACb;IACA,IAAIC,QAAQ,GAAGD,SAAS,CAACC,QAAQ;IACjC,IAAIC,WAAW,GAAGF,SAAS,CAACE,WAAW;IACvC,IAAIC,KAAK,GAAGH,SAAS,CAACG,KAAK;IAC3B,IAAIC,IAAI,GAAGJ,SAAS,CAACI,IAAI;IACzB;IACA,IAAI,CAACD,KAAK,IAAI,CAACC,IAAI,EAAE;MACnB,OAAO,IAAI;IACb;IACA,IAAIhC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IAC7B,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC/B,OAAOgC,KAAK,CAACjC,QAAQ,EAAE+B,KAAK,EAAE,UAAU,CAAC,IAAIE,KAAK,CAACjC,QAAQ,EAAE+B,KAAK,EAAE,SAAS,CAAC,IAAIE,KAAK,CAACjC,QAAQ,EAAE+B,KAAK,EAAE,OAAO,EAAE,gBAAgB,CAAC,IAAIE,KAAK,CAACjC,QAAQ,EAAE+B,KAAK,EAAE,MAAM,CAAC,IAAIE,KAAK,CAACjC,QAAQ,EAAE+B,KAAK,EAAE,IAAI,CAAC,IAAIE,KAAK,CAAChC,SAAS,EAAE6B,WAAW,EAAE,MAAM,CAAC,IAAIG,KAAK,CAAChC,SAAS,EAAE6B,WAAW,EAAE,WAAW,CAAC,IAAIG,KAAK,CAAChC,SAAS,EAAE6B,WAAW,EAAE,UAAU,CAAC,KAAK,CAACE,IAAI,CAACE,qBAAqB,IAAIF,IAAI,CAACE,qBAAqB,CAACP,SAAS,EAAE5B,KAAK,CAACG,UAAU,EAAE2B,QAAQ,EAAEC,WAAW,CAAC,CAAC;IAC5b,SAASG,KAAKA,CAAClC,KAAK,EAAEoC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAE;MAC5C,OAAOtC,KAAK,CAACqC,IAAI,CAAC,IAAI,IAAI,IAAID,IAAI,CAACE,UAAU,IAAID,IAAI,CAAC,KAAKrC,KAAK,CAACqC,IAAI,CAAC;IACxE;EACF,CAAC;EACDxC,gBAAgB,CAACC,SAAS,CAACyC,YAAY,GAAG,YAAY;IACpD;IACA,IAAI,CAACV,SAAS,GAAG,IAAI;EACvB,CAAC;EACD,OAAOhC,gBAAgB;AACzB,CAAC,CAAC,CAAC;AACH,SAASA,gBAAgB;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}