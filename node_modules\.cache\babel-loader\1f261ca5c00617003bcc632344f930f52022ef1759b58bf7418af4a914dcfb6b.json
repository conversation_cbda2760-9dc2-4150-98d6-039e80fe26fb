{"ast": null, "code": "var _typeof = require(\"F:/\\u5E38\\u89C4\\u9879\\u76EE/\\u534E\\u901A\\u5B9D/adminweb/node_modules/@babel/runtime/helpers/typeof.js\")[\"default\"];\n//! moment.js locale configuration\n//! locale : Yoruba Nigeria [yo]\n//! author : Atolagbe Abisoye : https://github.com/andela-batolagbe\n\n;\n(function (global, factory) {\n  (typeof exports === \"undefined\" ? \"undefined\" : _typeof(exports)) === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var yo = moment.defineLocale('yo', {\n    months: 'Sẹ́rẹ́_Èrèlè_Ẹrẹ̀nà_Ìgbé_Èbibi_Òkùdu_Agẹmo_Ògún_Owewe_Ọ̀wàrà_Bélú_Ọ̀pẹ̀̀'.split('_'),\n    monthsShort: 'Sẹ́r_Èrl_Ẹrn_Ìgb_Èbi_Òkù_Agẹ_Ògú_Owe_Ọ̀wà_Bél_Ọ̀pẹ̀̀'.split('_'),\n    weekdays: 'Àìkú_Ajé_Ìsẹ́gun_Ọjọ́rú_Ọjọ́bọ_Ẹtì_Àbámẹ́ta'.split('_'),\n    weekdaysShort: 'Àìk_Ajé_Ìsẹ́_Ọjr_Ọjb_Ẹtì_Àbá'.split('_'),\n    weekdaysMin: 'Àì_Aj_Ìs_Ọr_Ọb_Ẹt_Àb'.split('_'),\n    longDateFormat: {\n      LT: 'h:mm A',\n      LTS: 'h:mm:ss A',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY h:mm A',\n      LLLL: 'dddd, D MMMM YYYY h:mm A'\n    },\n    calendar: {\n      sameDay: '[Ònì ni] LT',\n      nextDay: '[Ọ̀la ni] LT',\n      nextWeek: \"dddd [Ọsẹ̀ tón'bọ] [ni] LT\",\n      lastDay: '[Àna ni] LT',\n      lastWeek: 'dddd [Ọsẹ̀ tólọ́] [ni] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'ní %s',\n      past: '%s kọjá',\n      s: 'ìsẹjú aayá die',\n      ss: 'aayá %d',\n      m: 'ìsẹjú kan',\n      mm: 'ìsẹjú %d',\n      h: 'wákati kan',\n      hh: 'wákati %d',\n      d: 'ọjọ́ kan',\n      dd: 'ọjọ́ %d',\n      M: 'osù kan',\n      MM: 'osù %d',\n      y: 'ọdún kan',\n      yy: 'ọdún %d'\n    },\n    dayOfMonthOrdinalParse: /ọjọ́\\s\\d{1,2}/,\n    ordinal: 'ọjọ́ %d',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return yo;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "_typeof", "module", "require", "define", "amd", "moment", "yo", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["F:/常规项目/华通宝/adminweb/node_modules/moment/locale/yo.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Yoruba Nigeria [yo]\n//! author : Atolagbe Abisoye : https://github.com/andela-batolagbe\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var yo = moment.defineLocale('yo', {\n        months: 'Sẹ́rẹ́_Èrèlè_Ẹrẹ̀nà_Ìgbé_Èbibi_Òkùdu_Agẹmo_Ògún_Owewe_Ọ̀wàrà_Bélú_Ọ̀pẹ̀̀'.split(\n            '_'\n        ),\n        monthsShort: 'Sẹ́r_Èrl_Ẹrn_Ìgb_Èbi_Òkù_Agẹ_Ògú_Owe_Ọ̀wà_Bél_Ọ̀pẹ̀̀'.split('_'),\n        weekdays: 'Àìkú_Ajé_Ìsẹ́gun_Ọjọ́rú_Ọjọ́bọ_Ẹtì_Àbámẹ́ta'.split('_'),\n        weekdaysShort: 'Àìk_Ajé_Ìsẹ́_Ọjr_Ọjb_Ẹtì_Àbá'.split('_'),\n        weekdaysMin: 'Àì_Aj_Ìs_Ọr_Ọb_Ẹt_Àb'.split('_'),\n        longDateFormat: {\n            LT: 'h:mm A',\n            LTS: 'h:mm:ss A',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY h:mm A',\n            LLLL: 'dddd, D MMMM YYYY h:mm A',\n        },\n        calendar: {\n            sameDay: '[Ònì ni] LT',\n            nextDay: '[Ọ̀la ni] LT',\n            nextWeek: \"dddd [Ọsẹ̀ tón'bọ] [ni] LT\",\n            lastDay: '[Àna ni] LT',\n            lastWeek: 'dddd [Ọsẹ̀ tólọ́] [ni] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'ní %s',\n            past: '%s kọjá',\n            s: 'ìsẹjú aayá die',\n            ss: 'aayá %d',\n            m: 'ìsẹjú kan',\n            mm: 'ìsẹjú %d',\n            h: 'wákati kan',\n            hh: 'wákati %d',\n            d: 'ọjọ́ kan',\n            dd: 'ọjọ́ %d',\n            M: 'osù kan',\n            MM: 'osù %d',\n            y: 'ọdún kan',\n            yy: 'ọdún %d',\n        },\n        dayOfMonthOrdinalParse: /ọjọ́\\s\\d{1,2}/,\n        ordinal: 'ọjọ́ %d',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return yo;\n\n})));\n"], "mappings": ";AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,QAAOC,OAAO,iCAAAC,OAAA,CAAPD,OAAO,OAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGJ,OAAO,CAACI,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEL,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACQ,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,yFAAyF,CAACC,KAAK,CACnG,GACJ,CAAC;IACDC,WAAW,EAAE,+DAA+D,CAACD,KAAK,CAAC,GAAG,CAAC;IACvFE,QAAQ,EAAE,sDAAsD,CAACF,KAAK,CAAC,GAAG,CAAC;IAC3EG,aAAa,EAAE,qCAAqC,CAACH,KAAK,CAAC,GAAG,CAAC;IAC/DI,WAAW,EAAE,0BAA0B,CAACJ,KAAK,CAAC,GAAG,CAAC;IAClDK,cAAc,EAAE;MACZC,EAAE,EAAE,QAAQ;MACZC,GAAG,EAAE,WAAW;MAChBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,oBAAoB;MACzBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,6BAA6B;MACvCC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,4BAA4B;MACtCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,mBAAmB;MACtBC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,eAAe;IACvCC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOzC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}