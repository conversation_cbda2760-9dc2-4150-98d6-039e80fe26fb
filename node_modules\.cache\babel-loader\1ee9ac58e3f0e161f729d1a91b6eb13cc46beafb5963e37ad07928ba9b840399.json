{"ast": null, "code": "import \"core-js/modules/es.number.constructor.js\";\nimport 'quill/dist/quill.core.css';\nimport 'quill/dist/quill.snow.css';\nimport { quillEditor } from 'vue-quill-editor';\nvar __default__ = {\n  name: 'Editor',\n  components: {\n    quillEditor: quillEditor\n  },\n  props: {\n    value: {\n      type: String,\n      \"default\": ''\n    },\n    disabled: {\n      type: Boolean,\n      \"default\": false\n    },\n    height: {\n      type: [Number, String],\n      \"default\": 500\n    }\n  },\n  data: function data() {\n    return {\n      content: '',\n      editorOption: {\n        placeholder: '请输入内容',\n        theme: 'snow',\n        modules: {\n          toolbar: [['bold', 'italic', 'underline', 'strike'], ['blockquote', 'code-block'], [{\n            'header': 1\n          }, {\n            'header': 2\n          }], [{\n            'list': 'ordered'\n          }, {\n            'list': 'bullet'\n          }], [{\n            'indent': '-1'\n          }, {\n            'indent': '+1'\n          }], [{\n            'direction': 'rtl'\n          }], [{\n            'size': ['small', false, 'large', 'huge']\n          }], [{\n            'header': [1, 2, 3, 4, 5, 6, false]\n          }], [{\n            'color': []\n          }, {\n            'background': []\n          }], [{\n            'font': []\n          }], [{\n            'align': []\n          }], ['link', 'image', 'video'], ['clean']]\n        }\n      }\n    };\n  },\n  watch: {\n    value: {\n      handler: function handler(val) {\n        if (val !== this.content) {\n          this.content = val;\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    onEditorChange: function onEditorChange(_ref) {\n      var html = _ref.html;\n      this.$emit('input', html);\n    }\n  }\n};\nimport { useCssVars as _useCssVars } from 'vue';\nvar __injectCSSVars__ = function __injectCSSVars__() {\n  _useCssVars(function (_vm, _setup) {\n    return {\n      \"7480c5e0-height____px_\": _vm.height + 'px'\n    };\n  });\n};\nvar __setup__ = __default__.setup;\n__default__.setup = __setup__ ? function (props, ctx) {\n  __injectCSSVars__();\n  return __setup__(props, ctx);\n} : __injectCSSVars__;\nexport default __default__;", "map": {"version": 3, "names": ["quill<PERSON><PERSON>or", "__default__", "name", "components", "props", "value", "type", "String", "disabled", "Boolean", "height", "Number", "data", "content", "editorOption", "placeholder", "theme", "modules", "toolbar", "watch", "handler", "val", "immediate", "methods", "onEditorChange", "_ref", "html", "$emit"], "sources": ["src/components/Editor/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"editor-container\">\r\n    <quill-editor\r\n      v-model=\"content\"\r\n      ref=\"quillEditor\"\r\n      :options=\"editorOption\"\r\n      :disabled=\"disabled\"\r\n      @change=\"onEditorChange\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport 'quill/dist/quill.core.css'\r\nimport 'quill/dist/quill.snow.css'\r\nimport { quillEditor } from 'vue-quill-editor'\r\n\r\nexport default {\r\n  name: 'Editor',\r\n  components: {\r\n    quillEditor\r\n  },\r\n  props: {\r\n    value: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    height: {\r\n      type: [Number, String],\r\n      default: 500\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      content: '',\r\n      editorOption: {\r\n        placeholder: '请输入内容',\r\n        theme: 'snow',\r\n        modules: {\r\n          toolbar: [\r\n            ['bold', 'italic', 'underline', 'strike'],\r\n            ['blockquote', 'code-block'],\r\n            [{ 'header': 1 }, { 'header': 2 }],\r\n            [{ 'list': 'ordered' }, { 'list': 'bullet' }],\r\n            [{ 'indent': '-1' }, { 'indent': '+1' }],\r\n            [{ 'direction': 'rtl' }],\r\n            [{ 'size': ['small', false, 'large', 'huge'] }],\r\n            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],\r\n            [{ 'color': [] }, { 'background': [] }],\r\n            [{ 'font': [] }],\r\n            [{ 'align': [] }],\r\n            ['link', 'image', 'video'],\r\n            ['clean']\r\n          ]\r\n        }\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val !== this.content) {\r\n          this.content = val\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  methods: {\r\n    onEditorChange({ html }) {\r\n      this.$emit('input', html)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.editor-container {\r\n  border: 1px solid #DCDFE6;\r\n  border-radius: 4px;\r\n  \r\n  ::v-deep .ql-container {\r\n    height: v-bind(height + 'px');\r\n  }\r\n  \r\n  ::v-deep .ql-toolbar.ql-snow,\r\n  ::v-deep .ql-container.ql-snow {\r\n    border: none;\r\n  }\r\n  \r\n  ::v-deep .ql-toolbar.ql-snow {\r\n    border-bottom: 1px solid #DCDFE6;\r\n    padding: 8px;\r\n  }\r\n\r\n  ::v-deep .ql-editor {\r\n    padding: 15px;\r\n    min-height: 300px;\r\n    font-size: 14px;\r\n    line-height: 1.6;\r\n  }\r\n}\r\n</style> "], "mappings": ";AAaA;AACA;AACA,SAAAA,WAAA;AAEA,IAAAC,WAAA;EACAC,IAAA;EACAC,UAAA;IACAH,WAAA,EAAAA;EACA;EACAI,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACA;IACA;IACAC,QAAA;MACAF,IAAA,EAAAG,OAAA;MACA;IACA;IACAC,MAAA;MACAJ,IAAA,GAAAK,MAAA,EAAAJ,MAAA;MACA;IACA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,YAAA;QACAC,WAAA;QACAC,KAAA;QACAC,OAAA;UACAC,OAAA,GACA,2CACA,8BACA;YAAA;UAAA;YAAA;UAAA,IACA;YAAA;UAAA;YAAA;UAAA,IACA;YAAA;UAAA;YAAA;UAAA,IACA;YAAA;UAAA,IACA;YAAA;UAAA,IACA;YAAA;UAAA,IACA;YAAA;UAAA;YAAA;UAAA,IACA;YAAA;UAAA,IACA;YAAA;UAAA,IACA,4BACA;QAEA;MACA;IACA;EACA;EACAC,KAAA;IACAd,KAAA;MACAe,OAAA,WAAAA,QAAAC,GAAA;QACA,IAAAA,GAAA,UAAAR,OAAA;UACA,KAAAA,OAAA,GAAAQ,GAAA;QACA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAC,IAAA;MAAA,IAAAC,IAAA,GAAAD,IAAA,CAAAC,IAAA;MACA,KAAAC,KAAA,UAAAD,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}