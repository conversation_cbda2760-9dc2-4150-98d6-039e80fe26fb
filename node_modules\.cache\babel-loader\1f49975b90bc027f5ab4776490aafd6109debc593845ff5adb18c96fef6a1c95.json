{"ast": null, "code": "import request from '@/utils/request';\n\n// 获取链端参数\nexport function getChainConfig(id) {\n  return request({\n    url: '/chain/config',\n    method: 'get'\n  });\n}\n\n// 修改链端参数\nexport function updateChainConfig(data) {\n  return request({\n    url: '/chain/config',\n    method: 'put',\n    data: data\n  });\n}\n\n// 解密私钥\nexport function decryptPrivateKey(id) {\n  return request({\n    url: '/chain/config/decrypt',\n    method: 'post',\n    data: {\n      id: id\n    }\n  });\n}", "map": {"version": 3, "names": ["request", "getChainConfig", "id", "url", "method", "updateChainConfig", "data", "decryptPrivateKey"], "sources": ["F:/常规项目/区块链项目/交易所项目/adminweb/src/api/system/chainConfig.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取链端参数\r\nexport function getChainConfig(id) {\r\n  return request({\r\n    url: '/chain/config',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 修改链端参数\r\nexport function updateChainConfig(data) {\r\n  return request({\r\n    url: '/chain/config',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 解密私钥\r\nexport function decryptPrivateKey(id) {\r\n  return request({\r\n    url: '/chain/config/decrypt',\r\n    method: 'post',\r\n    data: { id }\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,cAAcA,CAACC,EAAE,EAAE;EACjC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EACtC,OAAON,OAAO,CAAC;IACbG,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbE,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,iBAAiBA,CAACL,EAAE,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdE,IAAI,EAAE;MAAEJ,EAAE,EAAFA;IAAG;EACb,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}