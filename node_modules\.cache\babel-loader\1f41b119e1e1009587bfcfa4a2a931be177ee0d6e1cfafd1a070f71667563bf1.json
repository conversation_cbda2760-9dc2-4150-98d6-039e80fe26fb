{"ast": null, "code": "import request from '@/utils/request';\n\n// 获取公告列表\nexport function getNoticeList(params) {\n  return request({\n    url: '/notice/list',\n    method: 'get',\n    params: params\n  });\n}\n\n// 获取公告详情\nexport function getNoticeDetail(id) {\n  return request({\n    url: \"/notice/\".concat(id),\n    method: 'get'\n  });\n}\n\n// 新增或修改公告\nexport function saveNotice(data) {\n  return request({\n    url: '/notice/save',\n    method: 'post',\n    data: data\n  });\n}\n\n// 更新公告状态\nexport function updateNoticeStatus(id, status) {\n  return request({\n    url: \"/notice/status/\".concat(id),\n    method: 'put',\n    params: {\n      status: status\n    }\n  });\n}\n\n// 删除公告\nexport function deleteNotice(id) {\n  return request({\n    url: \"/notice/\".concat(id),\n    method: 'delete'\n  });\n}", "map": {"version": 3, "names": ["request", "getNoticeList", "params", "url", "method", "getNoticeDetail", "id", "concat", "saveNotice", "data", "updateNoticeStatus", "status", "deleteNotice"], "sources": ["E:/新项目/adminweb/src/api/notice/index.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取公告列表\r\nexport function getNoticeList(params) {\r\n  return request({\r\n    url: '/notice/list',\r\n    method: 'get',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取公告详情\r\nexport function getNoticeDetail(id) {\r\n  return request({\r\n    url: `/notice/${id}`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增或修改公告\r\nexport function saveNotice(data) {\r\n  return request({\r\n    url: '/notice/save',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 更新公告状态\r\nexport function updateNoticeStatus(id, status) {\r\n  return request({\r\n    url: `/notice/status/${id}`,\r\n    method: 'put',\r\n    params: { status }\r\n  })\r\n}\r\n\r\n// 删除公告\r\nexport function deleteNotice(id) {\r\n  return request({\r\n    url: `/notice/${id}`,\r\n    method: 'delete'\r\n  })\r\n} "], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAOF,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbF,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,eAAeA,CAACC,EAAE,EAAE;EAClC,OAAON,OAAO,CAAC;IACbG,GAAG,aAAAI,MAAA,CAAaD,EAAE,CAAE;IACpBF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASI,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOT,OAAO,CAAC;IACbG,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,kBAAkBA,CAACJ,EAAE,EAAEK,MAAM,EAAE;EAC7C,OAAOX,OAAO,CAAC;IACbG,GAAG,oBAAAI,MAAA,CAAoBD,EAAE,CAAE;IAC3BF,MAAM,EAAE,KAAK;IACbF,MAAM,EAAE;MAAES,MAAM,EAANA;IAAO;EACnB,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACN,EAAE,EAAE;EAC/B,OAAON,OAAO,CAAC;IACbG,GAAG,aAAAI,MAAA,CAAaD,EAAE,CAAE;IACpBF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}