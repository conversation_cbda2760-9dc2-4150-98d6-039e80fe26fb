{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport request from '@/utils/request';\n\n// 获取转账记录列表\nexport function getTransferList(params) {\n  console.log('发送请求参数:', params);\n  return request({\n    url: '/finance/transfer/list',\n    method: 'get',\n    params: params,\n    error: function error(_error) {\n      console.error('请求失败:', _error);\n      return Promise.reject(_error);\n    }\n  });\n}\n\n// 获取转账统计信息\nexport function getTransferStatistics() {\n  return request({\n    url: '/finance/transfer/statistics',\n    method: 'get'\n  });\n}\n\n// 导出转账记录\nexport function exportTransferRecord(params) {\n  return request({\n    url: '/finance/transfer/export',\n    method: 'get',\n    params: params,\n    responseType: 'blob'\n  });\n}", "map": {"version": 3, "names": ["request", "getTransferList", "params", "console", "log", "url", "method", "error", "Promise", "reject", "getTransferStatistics", "exportTransferRecord", "responseType"], "sources": ["G:/备份9/adminweb/src/api/finance/transfer.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取转账记录列表\r\nexport function getTransferList(params) {\r\n  console.log('发送请求参数:', params)\r\n  return request({\r\n    url: '/finance/transfer/list',\r\n    method: 'get',\r\n    params,\r\n    error: (error) => {\r\n      console.error('请求失败:', error)\r\n      return Promise.reject(error)\r\n    }\r\n  })\r\n}\r\n\r\n// 获取转账统计信息\r\nexport function getTransferStatistics() {\r\n  return request({\r\n    url: '/finance/transfer/statistics',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 导出转账记录\r\nexport function exportTransferRecord(params) {\r\n  return request({\r\n    url: '/finance/transfer/export',\r\n    method: 'get',\r\n    params,\r\n    responseType: 'blob'\r\n  })\r\n} "], "mappings": ";AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAO,SAASC,eAAeA,CAACC,MAAM,EAAE;EACtCC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEF,MAAM,CAAC;EAC9B,OAAOF,OAAO,CAAC;IACbK,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbJ,MAAM,EAANA,MAAM;IACNK,KAAK,EAAE,SAAPA,KAAKA,CAAGA,MAAK,EAAK;MAChBJ,OAAO,CAACI,KAAK,CAAC,OAAO,EAAEA,MAAK,CAAC;MAC7B,OAAOC,OAAO,CAACC,MAAM,CAACF,MAAK,CAAC;IAC9B;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASG,qBAAqBA,CAAA,EAAG;EACtC,OAAOV,OAAO,CAAC;IACbK,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACA,OAAO,SAASK,oBAAoBA,CAACT,MAAM,EAAE;EAC3C,OAAOF,OAAO,CAAC;IACbK,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbJ,MAAM,EAANA,MAAM;IACNU,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}